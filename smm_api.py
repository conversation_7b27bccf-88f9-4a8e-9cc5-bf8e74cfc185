import requests
import json
import logging
import secrets
from typing import Dict, Any, Optional
from config import Config

logger = logging.getLogger(__name__)

def generate_memo() -> str:
    """Generate a random memo string exactly like fc9c2db8ff2c090c10d3ed106163251e"""
    # Generate 32 random hexadecimal characters (16 bytes = 32 hex chars)
    return secrets.token_hex(16).lower()

class SMMAPIClient:
    """Client for interacting with the SMM API"""
    
    def __init__(self):
        self.api_key = Config.SMM_API_KEY
        self.base_url = Config.SMM_API_BASE_URL
        self.session = requests.Session()
        
        # Set default headers for admin API
        self.session.headers.update({
            'Content-Type': 'application/json',
            'X-API-Key': self.api_key,
            'User-Agent': 'TelegramSMMBot/1.0'
        })
    
    def add_payment(self, user_id: str, amount: float, username: str, payment_method: str = 'KHQR') -> Dict[str, Any]:
        """
        Add payment/balance to user account via admin API

        Args:
            user_id: Telegram user ID (not used in API, just for logging)
            amount: Payment amount in panel currency
            username: SMM account username (must match existing account)
            payment_method: Payment method identifier (default: KHQR)

        Returns:
            API response as dictionary with success/error status
        """
        try:
            # Validate input parameters
            if not username or len(username.strip()) < 2:
                return {
                    'success': False,
                    'error': 'Invalid username',
                    'message': 'Username must be at least 2 characters long'
                }

            if amount <= 0:
                return {
                    'success': False,
                    'error': 'Invalid amount',
                    'message': 'Amount must be greater than 0'
                }

            # Generate memo for the transaction
            memo = generate_memo()

            logger.info(f"Adding payment for user {user_id}, username: {username}, amount: {amount}, memo: {memo}")

            # Prepare payment data according to API documentation
            payment_data = {
                'username': username.strip(),
                'amount': float(amount),
                'method': payment_method,
                'memo': memo,
                'affiliate_commission': False
            }

            # Make API request with correct headers and JSON data
            response = self.session.post(
                f"{self.base_url}/payments/add",
                json=payment_data,
                timeout=30
            )

            logger.info(f"API Response Status: {response.status_code}")
            logger.info(f"API Response Content-Type: {response.headers.get('content-type', 'Unknown')}")
            logger.info(f"API Response Body: {response.text}")

            # Handle different response codes
            if response.status_code == 200:
                # Success response
                try:
                    result = response.json()
                    logger.info(f"Payment successful: {result}")
                    return {
                        'success': True,
                        'data': result,
                        'message': 'Payment added successfully'
                    }
                except json.JSONDecodeError as e:
                    # API returns HTML admin interface on success, not JSON
                    logger.info(f"Payment successful - API returned HTML admin interface")
                    return {
                        'success': True,
                        'data': {
                            'payment_id': memo,
                            'username': username,
                            'amount': amount,
                            'method': payment_method,
                            'memo': memo,
                            'status': 'submitted'
                        },
                        'message': 'Payment submitted successfully'
                    }

            elif response.status_code == 400:
                # Validation error
                try:
                    error_data = response.json()
                    logger.error(f"Validation error: {error_data}")
                    return {
                        'success': False,
                        'error': error_data.get('error_message', 'Validation error'),
                        'message': f"Payment failed: {error_data.get('error_message', 'Invalid parameters')}"
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Validation error',
                        'message': 'Payment failed: Invalid parameters'
                    }

            elif response.status_code == 404:
                # User not found
                try:
                    error_data = response.json()
                    logger.error(f"User not found: {error_data}")
                    return {
                        'success': False,
                        'error': error_data.get('error_message', 'User not found'),
                        'message': f"Payment failed: Username '{username}' not found"
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'User not found',
                        'message': f"Payment failed: Username '{username}' not found"
                    }

            else:
                # Other error
                logger.error(f"Unexpected status code: {response.status_code}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'message': f'Payment failed with status {response.status_code}'
                }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to process payment'
            }
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse API response: {e}")
            return {
                'success': False,
                'error': 'Invalid API response',
                'message': 'Server returned invalid response'
            }
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'An unexpected error occurred'
            }

    def add_payment_by_user_id(self, telegram_user_id: str, amount: float, smm_user_id: int, payment_method: str = 'KHQR') -> Dict[str, Any]:
        """
        Add payment/balance to user account via admin API using SMM user ID

        Args:
            telegram_user_id: Telegram user ID (for logging)
            amount: Payment amount in panel currency
            smm_user_id: SMM panel user ID (numeric)
            payment_method: Payment method identifier (default: KHQR)

        Returns:
            API response as dictionary with success/error status
        """
        try:
            # Validate input parameters
            if not isinstance(smm_user_id, int) or smm_user_id <= 0:
                return {
                    'success': False,
                    'error': 'Invalid user ID',
                    'message': 'User ID must be a positive integer'
                }

            if amount <= 0:
                return {
                    'success': False,
                    'error': 'Invalid amount',
                    'message': 'Amount must be greater than 0'
                }

            # Generate memo for the transaction
            memo = generate_memo()

            logger.info(f"Adding payment for telegram user {telegram_user_id}, SMM user ID: {smm_user_id}, amount: {amount}, memo: {memo}")

            # Prepare payment data according to API documentation
            payment_data = {
                'user_id': smm_user_id,
                'amount': float(amount),
                'method': payment_method,
                'memo': memo,
                'affiliate_commission': False
            }

            # Make API request with correct headers and JSON data
            response = self.session.post(
                f"{self.base_url}/payments/add",
                json=payment_data,
                timeout=30
            )

            logger.info(f"API Response Status: {response.status_code}")
            logger.info(f"API Response Body: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info(f"Payment added successfully for SMM user ID {smm_user_id}: {result}")

                    return {
                        'success': True,
                        'data': result,
                        'message': 'Payment added successfully'
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Invalid response format',
                        'message': 'Server returned invalid response'
                    }

            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    logger.error(f"Validation error: {error_data}")
                    return {
                        'success': False,
                        'error': error_data.get('error_message', 'Validation error'),
                        'message': f"Payment failed: {error_data.get('error_message', 'Invalid parameters')}"
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Validation error',
                        'message': 'Payment failed: Invalid parameters'
                    }

            elif response.status_code == 404:
                try:
                    error_data = response.json()
                    logger.error(f"User not found: {error_data}")
                    return {
                        'success': False,
                        'error': error_data.get('error_message', 'User not found'),
                        'message': f"Payment failed: User ID '{smm_user_id}' not found"
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'User not found',
                        'message': f"Payment failed: User ID '{smm_user_id}' not found"
                    }
            else:
                logger.error(f"Unexpected status code: {response.status_code}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'message': f'Payment failed with status {response.status_code}'
                }

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to process payment'
            }
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'An unexpected error occurred'
            }

    def add_payment_by_email(self, telegram_user_id: str, amount: float, user_email: str, payment_method: str = 'KHQR') -> Dict[str, Any]:
        """
        Add payment/balance to user account via admin API using email

        Args:
            telegram_user_id: Telegram user ID (for logging)
            amount: Payment amount in panel currency
            user_email: SMM panel user email address
            payment_method: Payment method identifier (default: KHQR)

        Returns:
            API response as dictionary with success/error status
        """
        try:
            # Validate input parameters
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

            if not user_email or not re.match(email_pattern, user_email.strip()):
                return {
                    'success': False,
                    'error': 'Invalid email',
                    'message': 'Please provide a valid email address'
                }

            if amount <= 0:
                return {
                    'success': False,
                    'error': 'Invalid amount',
                    'message': 'Amount must be greater than 0'
                }

            # Generate memo for the transaction
            memo = generate_memo()

            logger.info(f"Adding payment for telegram user {telegram_user_id}, email: {user_email}, amount: {amount}, memo: {memo}")

            # Prepare payment data according to API documentation
            payment_data = {
                'user_email': user_email.strip().lower(),
                'amount': float(amount),
                'method': payment_method,
                'memo': memo,
                'affiliate_commission': False
            }

            # Make API request with correct headers and JSON data
            response = self.session.post(
                f"{self.base_url}/payments/add",
                json=payment_data,
                timeout=30
            )

            logger.info(f"API Response Status: {response.status_code}")
            logger.info(f"API Response Body: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info(f"Payment added successfully for email {user_email}: {result}")

                    return {
                        'success': True,
                        'data': result,
                        'message': 'Payment added successfully'
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Invalid response format',
                        'message': 'Server returned invalid response'
                    }

            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    logger.error(f"Validation error: {error_data}")
                    return {
                        'success': False,
                        'error': error_data.get('error_message', 'Validation error'),
                        'message': f"Payment failed: {error_data.get('error_message', 'Invalid parameters')}"
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Validation error',
                        'message': 'Payment failed: Invalid parameters'
                    }

            elif response.status_code == 404:
                try:
                    error_data = response.json()
                    logger.error(f"User not found: {error_data}")
                    return {
                        'success': False,
                        'error': error_data.get('error_message', 'User not found'),
                        'message': f"Payment failed: Email '{user_email}' not found"
                    }
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'User not found',
                        'message': f"Payment failed: Email '{user_email}' not found"
                    }
            else:
                logger.error(f"Unexpected status code: {response.status_code}")
                return {
                    'success': False,
                    'error': f'HTTP {response.status_code}',
                    'message': f'Payment failed with status {response.status_code}'
                }

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to process payment'
            }
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'An unexpected error occurred'
            }

    def get_user_balance(self, user_id: str) -> Dict[str, Any]:
        """
        Get account balance using standard SMM panel API

        Args:
            user_id: User identifier (not used in standard API)

        Returns:
            API response with balance information
        """
        try:
            response = self.session.post(
                f"https://chhean-smm.net/api/v2",
                data={'key': self.api_key, 'action': 'balance'},
                timeout=30
            )

            response.raise_for_status()
            result = response.json()

            return {
                'success': True,
                'data': result,
                'message': 'Balance retrieved successfully'
            }

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get user balance: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Failed to retrieve balance'
            }
    
    def test_connection(self) -> bool:
        """
        Test API connection and authentication using balance endpoint

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            # Test connection using balance endpoint
            response = self.session.post(
                f"https://chhean-smm.net/api/v2",
                data={'key': self.api_key, 'action': 'balance'},
                timeout=10
            )

            if response.status_code == 200:
                try:
                    result = response.json()
                    return 'balance' in result
                except:
                    return False
            return False

        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
