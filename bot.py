import telebot
import logging
from config import Config
from smm_api import SMMAPIClient
from utils import setup_logging, error_handler, validate_amount, format_currency, BotError

# Configure logging
setup_logging()
logger = logging.getLogger(__name__)

class TelegramSMMBot:
    def __init__(self):
        """Initialize the Telegram bot with SMM API integration"""
        try:
            # Validate configuration
            Config.validate_config()
            
            # Initialize bot
            self.bot = telebot.TeleBot(Config.TELEGRAM_BOT_TOKEN)
            
            # Initialize SMM API client
            self.smm_client = SMMAPIClient()
            
            # Register handlers
            self._register_handlers()
            self._register_callback_handlers()
            
            logger.info("Bot initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize bot: {e}")
            raise
    
    def _register_handlers(self):
        """Register all bot command and message handlers"""
        
        @self.bot.message_handler(commands=['start'])
        def handle_start(message):
            welcome_text = """
🤖 Welcome to SMM Payment Bot!

Available commands:
/start - Show this welcome message
/topup - Process payment for SMM services
/balance - Check SMM panel balance
/help - Get help and instructions

To get started, use /topup command.
            """
            self.bot.reply_to(message, welcome_text)
        
        @self.bot.message_handler(commands=['help'])
        def handle_help(message):
            help_text = """
📋 How to use this bot:

1. Use /topup command to start the payment process
2. Enter your SMM account username
3. Enter the payment amount
4. Confirm the payment details
5. Payment will be processed with KHQR method

Note: This processes payments for SMM services.
For support, contact the administrator.
            """
            self.bot.reply_to(message, help_text)
        
        @self.bot.message_handler(commands=['topup'])
        def handle_topup(message):
            """Handle top-up command"""
            try:
                # Ask for username first
                msg = self.bot.reply_to(
                    message,
                    "👤 Please enter your SMM account username:\n\nExample: your_username"
                )
                self.bot.register_next_step_handler(msg, self._process_topup_username)

            except Exception as e:
                logger.error(f"Error in topup handler: {e}")
                self.bot.reply_to(message, "❌ An error occurred. Please try again later.")

        @self.bot.message_handler(commands=['balance'])
        def handle_balance(message):
            """Handle balance check command"""
            try:
                user_id = str(message.from_user.id)

                # Show loading message
                loading_msg = self.bot.reply_to(message, "🔍 Checking your balance...")

                # Get balance from API
                result = self.smm_client.get_user_balance(user_id)

                if result['success']:
                    balance_info = result['data']
                    balance_text = f"💳 Your current balance: ${balance_info.get('balance', 'N/A')}"
                else:
                    balance_text = "❌ Unable to retrieve balance. Please try again later."

                # Update the loading message
                self.bot.edit_message_text(
                    balance_text,
                    chat_id=loading_msg.chat.id,
                    message_id=loading_msg.message_id
                )

            except Exception as e:
                logger.error(f"Error in balance handler: {e}")
                self.bot.reply_to(message, "❌ An error occurred while checking balance.")

    def _process_topup_username(self, message):
        """Process the username entered by user"""
        try:
            # Validate and sanitize username
            from utils import sanitize_user_input
            username = sanitize_user_input(message.text)

            if not username:
                self.bot.reply_to(message, "❌ Please enter a valid username.")
                return

            if len(username) < 3:
                self.bot.reply_to(message, "❌ Username must be at least 3 characters long.")
                return

            if len(username) > 50:
                self.bot.reply_to(message, "❌ Username must be less than 50 characters.")
                return

            # Store username in user session (we'll use a simple approach)
            # Ask for amount
            msg = self.bot.reply_to(
                message,
                f"💰 Username: {username}\n\nNow please enter the amount you want to top up (in USD):\n\nExample: 10.00"
            )

            # Pass username to the next handler
            self.bot.register_next_step_handler(msg, lambda m: self._process_topup_amount(m, username))

        except Exception as e:
            logger.error(f"Error processing username: {e}")
            self.bot.reply_to(message, "❌ An error occurred. Please try again.")

    def _process_topup_amount(self, message, username=None):
        """Process the top-up amount entered by user"""
        try:
            # Validate amount
            is_valid, amount, error_msg = validate_amount(message.text)

            if not is_valid:
                self.bot.reply_to(message, f"❌ {error_msg}")
                return

            # Ensure we have a username
            if not username:
                self.bot.reply_to(message, "❌ Username is missing. Please start over with /topup")
                return

            # Confirm the payment
            user_id = str(message.from_user.id)

            confirm_text = f"""
💰 Top-up Confirmation

SMM Username: {username}
Amount: {format_currency(amount)}
Payment Method: KHQR
Telegram User: {message.from_user.first_name or 'Unknown'}

Please confirm this payment by clicking the button below.
            """

            # Create inline keyboard for confirmation
            markup = telebot.types.InlineKeyboardMarkup()
            confirm_btn = telebot.types.InlineKeyboardButton(
                "✅ Confirm Payment",
                callback_data=f"confirm_payment:{user_id}:{amount}:{username}"
            )
            cancel_btn = telebot.types.InlineKeyboardButton(
                "❌ Cancel",
                callback_data="cancel_payment"
            )
            markup.add(confirm_btn, cancel_btn)

            self.bot.reply_to(message, confirm_text, reply_markup=markup)

        except ValueError:
            self.bot.reply_to(message, "❌ Please enter a valid number. Example: 10.00")
        except Exception as e:
            logger.error(f"Error processing topup amount: {e}")
            self.bot.reply_to(message, "❌ An error occurred. Please try again.")

    def _register_callback_handlers(self):
        """Register callback query handlers"""

        @self.bot.callback_query_handler(func=lambda call: True)
        def handle_callback(call):
            try:
                if call.data.startswith("confirm_payment:"):
                    # Parse payment data
                    parts = call.data.split(":")
                    if len(parts) >= 4:
                        _, user_id, amount, username = parts[0], parts[1], parts[2], ":".join(parts[3:])
                    else:
                        # Fallback for old format
                        _, user_id, amount = parts
                        username = "unknown"

                    amount = float(amount)

                    # Process the payment
                    self._process_payment(call, user_id, amount, username)

                elif call.data == "cancel_payment":
                    self.bot.edit_message_text(
                        "❌ Payment cancelled.",
                        chat_id=call.message.chat.id,
                        message_id=call.message.message_id
                    )

            except Exception as e:
                logger.error(f"Error handling callback: {e}")
                self.bot.answer_callback_query(call.id, "❌ An error occurred.")

    def _process_payment(self, call, user_id: str, amount: float, username: str):
        """Process the actual payment through SMM API"""
        try:
            # Show processing message
            self.bot.edit_message_text(
                "⏳ Processing your payment...",
                chat_id=call.message.chat.id,
                message_id=call.message.message_id
            )

            # Make API call with username
            result = self.smm_client.add_payment(user_id, amount, username)

            if result['success']:
                success_text = f"""
✅ Payment Processed Successfully!

SMM Username: {username}
Amount: {format_currency(amount)}
Payment Method: KHQR
Memo: {result['data'].get('memo', 'N/A')}
Payment ID: {result['data'].get('payment_id', 'N/A')}
Status: Processed

Your payment has been recorded and will be processed manually.
                """
                self.bot.edit_message_text(
                    success_text,
                    chat_id=call.message.chat.id,
                    message_id=call.message.message_id
                )
            else:
                error_text = f"""
❌ Payment Failed

SMM Username: {username}
Amount: {format_currency(amount)}
Payment Method: KHQR
Error: {result['message']}

Please try again or contact support.
                """
                self.bot.edit_message_text(
                    error_text,
                    chat_id=call.message.chat.id,
                    message_id=call.message.message_id
                )

            # Answer the callback query
            self.bot.answer_callback_query(call.id)

        except Exception as e:
            logger.error(f"Error processing payment: {e}")
            self.bot.edit_message_text(
                "❌ Payment processing failed. Please try again later.",
                chat_id=call.message.chat.id,
                message_id=call.message.message_id
            )
            self.bot.answer_callback_query(call.id, "❌ Payment failed.")

    def run(self):
        """Start the bot polling"""
        try:
            logger.info("Starting bot polling...")
            self.bot.infinity_polling(timeout=10, long_polling_timeout=5)
        except Exception as e:
            logger.error(f"Bot polling error: {e}")
            raise

if __name__ == "__main__":
    try:
        bot = TelegramSMMBot()
        bot.run()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {e}")
