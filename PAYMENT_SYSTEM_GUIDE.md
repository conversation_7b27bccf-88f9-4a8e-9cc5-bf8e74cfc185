# SMM Payment System Guide

## Overview

This guide explains how to add payment balance to users in the SMM panel using the Admin API v2. The system supports three methods of user identification and uses KHQR payment method with random memo strings.

## API Endpoint

**Base URL**: `https://chhean-smm.net/admin/adminapi/v2`  
**Endpoint**: `POST /payments/add`  
**Authentication**: API Key in headers (`X-API-Key`)

## User Identification Methods

The API supports three ways to identify users:

### 1. Username (Recommended - Currently Used)
```python
# Example usage
result = smm_client.add_payment(
    user_id="*********",      # Telegram user ID (for logging)
    amount=10.50,             # Payment amount in USD
    username="john_doe",      # SMM account username
    payment_method="KHQR"     # Payment method
)
```

### 2. User ID (Numeric)
```python
# Example usage
result = smm_client.add_payment_by_user_id(
    telegram_user_id="*********",  # Telegram user ID (for logging)
    amount=25.00,                  # Payment amount in USD
    smm_user_id=42,               # SMM panel user ID (numeric)
    payment_method="KHQR"         # Payment method
)
```

### 3. Email Address
```python
# Example usage
result = smm_client.add_payment_by_email(
    telegram_user_id="*********",     # Telegram user ID (for logging)
    amount=15.75,                     # Payment amount in USD
    user_email="<EMAIL>",    # SMM panel user email
    payment_method="KHQR"             # Payment method
)
```

## Request Format

### Required Fields
- **amount**: Payment amount in panel currency (must be > 0)
- **method**: Payment method name (3-300 characters)
- **User identifier**: One of:
  - `username`: SMM account username
  - `user_id`: SMM panel user ID (integer)
  - `user_email`: SMM panel user email

### Optional Fields
- **memo**: Payment memo for staff (max 300 characters) - Auto-generated
- **affiliate_commission**: Boolean (default: false)

## API Response Format

### Success Response (HTTP 200)
```json
{
  "success": true,
  "data": {
    "payment_id": 12345,
    "user_id": 42,
    "username": "john_doe",
    "email": "<EMAIL>",
    "balance": "125.50",
    "amount": {
      "value": 10.50,
      "currency_code": "USD",
      "formatted": "$10.50"
    },
    "method": "KHQR",
    "memo": "fc9c2db8ff2c090c10d3ed106163251e",
    "status": "completed"
  },
  "message": "Payment added successfully"
}
```

### Error Responses

#### Validation Error (HTTP 400)
```json
{
  "success": false,
  "error": "Validation error",
  "message": "Payment failed: Invalid parameters"
}
```

#### User Not Found (HTTP 404)
```json
{
  "success": false,
  "error": "User not found",
  "message": "Payment failed: Username 'john_doe' not found"
}
```

## Implementation Details

### Memo Generation
The system automatically generates random memo strings using 32 hexadecimal characters:
```python
def generate_memo() -> str:
    """Generate a random memo string exactly like fc9c2db8ff2c090c10d3ed106163251e"""
    return secrets.token_hex(16).lower()
```

### Input Validation
- **Username**: Must be at least 2 characters, stripped of whitespace
- **User ID**: Must be positive integer
- **Email**: Must match valid email pattern
- **Amount**: Must be greater than 0, converted to float

### Error Handling
The system includes comprehensive error handling for:
- Network errors (timeouts, connection issues)
- HTTP errors (400, 404, 500, etc.)
- JSON parsing errors
- Validation errors
- Unexpected exceptions

## Configuration

### Environment Variables
```bash
# Required
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
SMM_API_KEY=your_smm_api_key

# Optional
SMM_API_BASE_URL=https://chhean-smm.net/admin/adminapi/v2
```

### Config Class
```python
class Config:
    SMM_API_KEY = os.getenv('SMM_API_KEY')
    SMM_API_BASE_URL = os.getenv('SMM_API_BASE_URL', 'https://chhean-smm.net/admin/adminapi/v2')
    PAYMENTS_ADD_ENDPOINT = f"{SMM_API_BASE_URL}/payments/add"
```

## Usage Examples

### Basic Payment (Username)
```python
from smm_api import SMMAPIClient

# Initialize client
smm_client = SMMAPIClient()

# Add payment using username
result = smm_client.add_payment(
    user_id="*********",
    amount=50.00,
    username="customer123"
)

if result['success']:
    print(f"Payment successful! New balance: {result['data']['balance']}")
else:
    print(f"Payment failed: {result['message']}")
```

### Payment with User ID
```python
# Add payment using SMM user ID
result = smm_client.add_payment_by_user_id(
    telegram_user_id="*********",
    amount=25.00,
    smm_user_id=42
)
```

### Payment with Email
```python
# Add payment using email
result = smm_client.add_payment_by_email(
    telegram_user_id="*********",
    amount=75.00,
    user_email="<EMAIL>"
)
```

## Bot Integration

The Telegram bot integrates the payment system through the `/topup` command:

1. User enters `/topup`
2. Bot asks for SMM username
3. Bot asks for payment amount
4. Bot shows confirmation with details
5. User confirms payment
6. Bot processes payment via API
7. Bot shows success/error message

## Security Considerations

- API key is stored securely in environment variables
- Input validation prevents injection attacks
- Usernames are sanitized before processing
- All API communications use HTTPS
- Comprehensive logging for audit trails

## Troubleshooting

### Common Issues

1. **"User not found"**: Verify the username/email/ID exists in SMM panel
2. **"Validation error"**: Check amount is positive and username format
3. **"Network error"**: Check internet connection and API endpoint
4. **"Invalid API response"**: Check API key and endpoint configuration

### Logging
All operations are logged with details:
- Request parameters
- API responses
- Error messages
- Transaction memos

Check `bot.log` file for detailed debugging information.
