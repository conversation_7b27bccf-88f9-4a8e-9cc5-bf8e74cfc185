#!/usr/bin/env python3
"""
SMM Payment System Usage Examples

This script demonstrates how to use the SMM payment system to add balance to user accounts.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smm_api import SMMAPIClient
from utils import setup_logging, format_currency
import logging

def example_payment_by_username():
    """Example: Add payment using username"""
    print("💰 Example: Adding payment by username")
    print("-" * 40)
    
    # Initialize the SMM API client
    client = SMMAPIClient()
    
    # Payment details
    telegram_user_id = "*********"  # Telegram user ID (for logging)
    amount = 25.50                  # Payment amount in USD
    username = "customer123"        # SMM account username
    payment_method = "KHQR"         # Payment method
    
    print(f"Telegram User: {telegram_user_id}")
    print(f"SMM Username: {username}")
    print(f"Amount: {format_currency(amount)}")
    print(f"Method: {payment_method}")
    print()
    
    # Add the payment
    result = client.add_payment(
        user_id=telegram_user_id,
        amount=amount,
        username=username,
        payment_method=payment_method
    )
    
    # Handle the result
    if result['success']:
        print("✅ Payment successful!")
        data = result['data']
        print(f"   Payment ID: {data.get('payment_id', 'N/A')}")
        print(f"   New Balance: {data.get('balance', 'N/A')}")
        print(f"   Memo: {data.get('memo', 'N/A')}")
    else:
        print("❌ Payment failed!")
        print(f"   Error: {result['error']}")
        print(f"   Message: {result['message']}")
    
    print()

def example_payment_by_user_id():
    """Example: Add payment using SMM user ID"""
    print("💰 Example: Adding payment by user ID")
    print("-" * 40)
    
    # Initialize the SMM API client
    client = SMMAPIClient()
    
    # Payment details
    telegram_user_id = "987654321"  # Telegram user ID (for logging)
    amount = 50.00                  # Payment amount in USD
    smm_user_id = 42               # SMM panel user ID (numeric)
    payment_method = "KHQR"         # Payment method
    
    print(f"Telegram User: {telegram_user_id}")
    print(f"SMM User ID: {smm_user_id}")
    print(f"Amount: {format_currency(amount)}")
    print(f"Method: {payment_method}")
    print()
    
    # Add the payment
    result = client.add_payment_by_user_id(
        telegram_user_id=telegram_user_id,
        amount=amount,
        smm_user_id=smm_user_id,
        payment_method=payment_method
    )
    
    # Handle the result
    if result['success']:
        print("✅ Payment successful!")
        data = result['data']
        print(f"   Payment ID: {data.get('payment_id', 'N/A')}")
        print(f"   Username: {data.get('username', 'N/A')}")
        print(f"   New Balance: {data.get('balance', 'N/A')}")
        print(f"   Memo: {data.get('memo', 'N/A')}")
    else:
        print("❌ Payment failed!")
        print(f"   Error: {result['error']}")
        print(f"   Message: {result['message']}")
    
    print()

def example_payment_by_email():
    """Example: Add payment using email address"""
    print("💰 Example: Adding payment by email")
    print("-" * 40)
    
    # Initialize the SMM API client
    client = SMMAPIClient()
    
    # Payment details
    telegram_user_id = "555666777"      # Telegram user ID (for logging)
    amount = 15.75                      # Payment amount in USD
    user_email = "<EMAIL>" # SMM panel user email
    payment_method = "KHQR"             # Payment method
    
    print(f"Telegram User: {telegram_user_id}")
    print(f"Email: {user_email}")
    print(f"Amount: {format_currency(amount)}")
    print(f"Method: {payment_method}")
    print()
    
    # Add the payment
    result = client.add_payment_by_email(
        telegram_user_id=telegram_user_id,
        amount=amount,
        user_email=user_email,
        payment_method=payment_method
    )
    
    # Handle the result
    if result['success']:
        print("✅ Payment successful!")
        data = result['data']
        print(f"   Payment ID: {data.get('payment_id', 'N/A')}")
        print(f"   Username: {data.get('username', 'N/A')}")
        print(f"   New Balance: {data.get('balance', 'N/A')}")
        print(f"   Memo: {data.get('memo', 'N/A')}")
    else:
        print("❌ Payment failed!")
        print(f"   Error: {result['error']}")
        print(f"   Message: {result['message']}")
    
    print()

def example_balance_check():
    """Example: Check user balance"""
    print("💳 Example: Checking balance")
    print("-" * 40)
    
    # Initialize the SMM API client
    client = SMMAPIClient()
    
    # Check balance (this uses the standard SMM API, not admin API)
    result = client.get_user_balance("*********")
    
    if result['success']:
        print("✅ Balance retrieved successfully!")
        balance_data = result['data']
        print(f"   Balance: {balance_data.get('balance', 'N/A')}")
    else:
        print("❌ Failed to retrieve balance!")
        print(f"   Error: {result['error']}")
        print(f"   Message: {result['message']}")
    
    print()

def example_error_handling():
    """Example: Error handling scenarios"""
    print("⚠️  Example: Error handling")
    print("-" * 40)
    
    client = SMMAPIClient()
    
    # Example 1: Invalid amount
    print("Testing invalid amount...")
    result = client.add_payment(
        user_id="*********",
        amount=-10.00,  # Negative amount
        username="test_user"
    )
    print(f"Result: {result['message']}")
    print()
    
    # Example 2: Invalid username
    print("Testing invalid username...")
    result = client.add_payment(
        user_id="*********",
        amount=10.00,
        username=""  # Empty username
    )
    print(f"Result: {result['message']}")
    print()
    
    # Example 3: Invalid email
    print("Testing invalid email...")
    result = client.add_payment_by_email(
        telegram_user_id="*********",
        amount=10.00,
        user_email="not-an-email"  # Invalid email format
    )
    print(f"Result: {result['message']}")
    print()

def main():
    """Main example function"""
    print("🚀 SMM Payment System Examples")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    
    # Check configuration
    if not os.getenv('SMM_API_KEY'):
        print("❌ SMM_API_KEY environment variable not set!")
        print("   Please set your API key in .env file")
        return
    
    print(f"🔑 Using API Key: {'*' * 20}{os.getenv('SMM_API_KEY')[-4:]}")
    print()
    
    # Run examples
    try:
        # Basic examples (these will fail without valid user data, but show the usage)
        example_payment_by_username()
        example_payment_by_user_id()
        example_payment_by_email()
        example_balance_check()
        
        # Error handling examples (these should work)
        example_error_handling()
        
        print("✅ All examples completed!")
        print("\n📝 Note: Payment examples may fail if the usernames/IDs/emails don't exist.")
        print("   Replace the example data with real user information to test actual payments.")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")
        logging.error(f"Example error: {e}")

if __name__ == "__main__":
    main()
