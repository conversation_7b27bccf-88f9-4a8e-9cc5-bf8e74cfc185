import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for the Telegram bot and SMM API"""
    
    # Telegram Bot Configuration
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
    
    # SMM API Configuration
    SMM_API_KEY = os.getenv('SMM_API_KEY')
    SMM_API_BASE_URL = os.getenv('SMM_API_BASE_URL', 'https://chhean-smm.net/admin/adminapi/v2')
    
    # API Endpoints
    PAYMENTS_ADD_ENDPOINT = f"{SMM_API_BASE_URL}/payments/add"
    
    @classmethod
    def validate_config(cls):
        """Validate that all required configuration is present"""
        missing_vars = []
        
        if not cls.TELEGRAM_BOT_TOKEN:
            missing_vars.append('TELEGRAM_BOT_TOKEN')
        
        if not cls.SMM_API_KEY:
            missing_vars.append('SMM_API_KEY')
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
        
        return True
