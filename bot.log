2025-07-08 21:46:54,685 - __main__ - INFO - bot.py:28 - <PERSON><PERSON> initialized successfully
2025-07-08 21:46:54,686 - __main__ - INFO - bot.py:235 - Starting bot polling...
2025-07-08 21:50:48,084 - __main__ - INFO - bot.py:28 - <PERSON><PERSON> initialized successfully
2025-07-08 21:50:48,084 - __main__ - INFO - bot.py:284 - Starting bot polling...
2025-07-08 21:56:58,995 - __main__ - INFO - bot.py:28 - <PERSON><PERSON> initialized successfully
2025-07-08 21:56:58,995 - __main__ - INFO - bot.py:288 - Starting bot polling...
2025-07-08 22:03:55,405 - __main__ - INFO - bot.py:28 - <PERSON><PERSON> initialized successfully
2025-07-08 22:03:55,405 - __main__ - INFO - bot.py:288 - Starting bot polling...
2025-07-08 22:04:21,020 - smm_api - INFO - smm_api.py:57 - Adding payment for user 1630035459, username: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amount: 10.0, memo: 671378e201fcb57ad22b75281f548efa
2025-07-08 22:04:22,384 - smm_api - INFO - smm_api.py:67 - API Response Status: 200
2025-07-08 22:04:22,385 - smm_api - DEBUG - smm_api.py:68 - API Response Body:     <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="csrf-param" content="_csrf_admin">
<meta name="csrf-token" content="IijeLbjnQeWS6Ns1sl-nIYtXWFnppo6hROUivL6M7MoXQul89N8DtKaRt031BZRGxj4KP6_F9u80tBSP8Mutkg==">
        <title></title>
                    <link rel="shortcut icon" type="image/ico" href=""/>
                <link href="/react_apps/admin/static/css/main.6ec61889d8c5d015738f.css" rel="stylesheet">        <script> window.modules = {}; </script>
    </head>

    <body class="">

    
    <noscript>You need to enable JavaScript to run this app.</noscript>

    
<script>
    window.appConfig = {};
    window.appConfig = {"second_factor":true,"captcha_required":false,"captcha_site_key":"6Len5bUaAAAAAArhgiBHe_DhW5XsSygGh5JfR0yF","captcha_type":"g-recaptcha","is_dev":false,"posthog":{"apikey":"phc_dyafmP9dhK1Ix71AaOFYhGBGPzpWY6FtPav5gD5stlY","domain":"chhean-smm.net","admin":null,"params":{"api_host":"https:\/\/app.posthog.com","autocapture":false,"capture_pageview":false}},"websocket_url":null,"reserve_websocket_url":null};
</script>

<div id="root">
    <div style="position: absolute; top: 0; left: 0; width: 100%; min-height: 100vh; display: flex; align-items: center; justify-content: center">
        <div class="spinner"></div>
    </div>
    <style>
        .spinner {
            display: inline-block;
            width: 32px;
            height: 32px;
            --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg%3E%3Ccircle cx='3' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='21' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='21' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='3' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='5.64' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='5.64' r='2' fill='%23000'/%3E%3CanimateTransform attributeName='transform' dur='1.5s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/g%3E%3C/svg%3E");
            background-color: currentColor;
            -webkit-mask-image: var(--svg);
            mask-image: var(--svg);
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: 100% 100%;
            mask-size: 100% 100%;
        }
    </style>
</div>
    <script src="/builds/posthog/js/posthog.7e8c35b8acc75587bb73.js"></script>
<script src="/builds/posthog/js/posthog-front.js"></script>
<script src="/react_apps/admin/static/js/main.46262c4695c08d79e7bd.js"></script>
<script src="/react_apps/admin/static/js/vendors.e1966d222e648233c5e1.js"></script>
<script>window.modules.customController = {"auth":0};</script>
    </body>
    </html>

2025-07-08 22:04:22,385 - smm_api - ERROR - smm_api.py:83 - API request failed: Expecting value: line 1 column 5 (char 4)
2025-07-08 22:04:57,218 - TeleBot - ERROR - __init__.py:960 - Infinity polling exception: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))
2025-07-08 22:04:57,224 - TeleBot - ERROR - __init__.py:962 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1428, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1428, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
urllib3.exceptions.ProtocolError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 955, in infinity_polling
    self.polling(non_stop=True, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 logger_level=logger_level, allowed_updates=allowed_updates, restart_on_change=False,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 *args, **kwargs)
                 ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1043, in polling
    self.__threaded_polling(non_stop=non_stop, interval=interval, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                            logger_level=logger_level, allowed_updates=allowed_updates)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1118, in __threaded_polling
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 156, in _make_request
    result = _get_req_session().request(
        method, request_url, params=params, files=files,
        timeout=(connect_timeout, read_timeout), proxies=proxy)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 501, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))

2025-07-08 22:08:41,206 - TeleBot - ERROR - __init__.py:960 - Infinity polling exception: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))
2025-07-08 22:08:41,211 - TeleBot - ERROR - __init__.py:962 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1428, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1428, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
urllib3.exceptions.ProtocolError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 955, in infinity_polling
    self.polling(non_stop=True, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 logger_level=logger_level, allowed_updates=allowed_updates, restart_on_change=False,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 *args, **kwargs)
                 ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1043, in polling
    self.__threaded_polling(non_stop=non_stop, interval=interval, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                            logger_level=logger_level, allowed_updates=allowed_updates)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1118, in __threaded_polling
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1074, in __threaded_polling
    self.worker_pool.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 147, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 6801, in _run_middlewares_and_handler
    result = handler['function'](message)
  File "C:\tg bot smm\bot.py", line 50, in handle_start
    self.bot.reply_to(message, welcome_text)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 4528, in reply_to
    return self.send_message(message.chat.id, text, reply_to_message_id=message.message_id, **kwargs)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1549, in send_message
    apihelper.send_message(
    ~~~~~~~~~~~~~~~~~~~~~~^
        self.token, chat_id, text, disable_web_page_preview, reply_to_message_id,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        reply_markup, parse_mode, disable_notification, timeout,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        entities, allow_sending_without_reply, protect_content=protect_content, message_thread_id=message_thread_id))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 264, in send_message
    return _make_request(token, method_url, params=payload, method='post')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 156, in _make_request
    result = _get_req_session().request(
        method, request_url, params=params, files=files,
        timeout=(connect_timeout, read_timeout), proxies=proxy)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 501, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))

2025-07-08 22:08:52,107 - __main__ - ERROR - bot.py:79 - Error in topup handler: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))
2025-07-08 22:09:44,958 - smm_api - INFO - smm_api.py:57 - Adding payment for user 1630035459, username: mlbbmoontod0, amount: 1.0, memo: 8f01d368d4209caab2cb6e456476a69c
2025-07-08 22:09:48,097 - smm_api - INFO - smm_api.py:67 - API Response Status: 200
2025-07-08 22:09:48,098 - smm_api - DEBUG - smm_api.py:68 - API Response Body:     <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="csrf-param" content="_csrf_admin">
<meta name="csrf-token" content="JzUtyVQUkpMwgBEfaIIg4XFmfIUNYGL_kUhD58PuohYSXxqYGCzQwgT5fWcv2BOGPA8u40sDGrHhGXXUjanjTg==">
        <title></title>
                    <link rel="shortcut icon" type="image/ico" href=""/>
                <link href="/react_apps/admin/static/css/main.6ec61889d8c5d015738f.css" rel="stylesheet">        <script> window.modules = {}; </script>
    </head>

    <body class="">

    
    <noscript>You need to enable JavaScript to run this app.</noscript>

    
<script>
    window.appConfig = {};
    window.appConfig = {"second_factor":true,"captcha_required":false,"captcha_site_key":"6Len5bUaAAAAAArhgiBHe_DhW5XsSygGh5JfR0yF","captcha_type":"g-recaptcha","is_dev":false,"posthog":{"apikey":"phc_dyafmP9dhK1Ix71AaOFYhGBGPzpWY6FtPav5gD5stlY","domain":"chhean-smm.net","admin":null,"params":{"api_host":"https:\/\/app.posthog.com","autocapture":false,"capture_pageview":false}},"websocket_url":null,"reserve_websocket_url":null};
</script>

<div id="root">
    <div style="position: absolute; top: 0; left: 0; width: 100%; min-height: 100vh; display: flex; align-items: center; justify-content: center">
        <div class="spinner"></div>
    </div>
    <style>
        .spinner {
            display: inline-block;
            width: 32px;
            height: 32px;
            --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg%3E%3Ccircle cx='3' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='21' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='21' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='3' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='5.64' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='5.64' r='2' fill='%23000'/%3E%3CanimateTransform attributeName='transform' dur='1.5s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/g%3E%3C/svg%3E");
            background-color: currentColor;
            -webkit-mask-image: var(--svg);
            mask-image: var(--svg);
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: 100% 100%;
            mask-size: 100% 100%;
        }
    </style>
</div>
    <script src="/builds/posthog/js/posthog-front.js"></script>
<script src="/builds/posthog/js/posthog.7e8c35b8acc75587bb73.js"></script>
<script src="/react_apps/admin/static/js/main.46262c4695c08d79e7bd.js"></script>
<script src="/react_apps/admin/static/js/vendors.e1966d222e648233c5e1.js"></script>
<script>window.modules.customController = {"auth":0};</script>
    </body>
    </html>

2025-07-08 22:09:48,098 - smm_api - ERROR - smm_api.py:83 - API request failed: Expecting value: line 1 column 5 (char 4)
2025-07-08 22:17:06,515 - __main__ - INFO - bot.py:28 - Bot initialized successfully
2025-07-08 22:17:06,515 - __main__ - INFO - bot.py:288 - Starting bot polling...
2025-07-08 22:24:31,937 - __main__ - INFO - bot.py:28 - Bot initialized successfully
2025-07-08 22:24:31,938 - __main__ - INFO - bot.py:289 - Starting bot polling...
2025-07-08 22:24:35,611 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:24:35,613 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:24:39,371 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:24:39,372 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:24:40,126 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:24:40,127 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:24:44,139 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:24:44,140 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:24:45,144 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:24:45,145 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:24:49,650 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:24:49,652 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:24:51,155 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:24:51,156 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:24:56,663 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:24:56,664 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:24:59,202 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:24:59,203 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:25:02,959 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:25:02,960 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:25:07,465 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:25:07,467 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:25:11,224 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:25:11,227 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:25:19,730 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:25:19,731 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:25:23,492 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:25:23,493 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:25:40,000 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:25:40,002 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:25:43,761 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:25:43,763 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:16,270 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:16,271 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:20,137 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:20,138 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:32,226 - __main__ - INFO - bot.py:28 - Bot initialized successfully
2025-07-08 22:26:32,226 - __main__ - INFO - bot.py:289 - Starting bot polling...
2025-07-08 22:26:35,658 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:35,660 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:39,417 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:39,418 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:40,260 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:40,262 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:44,271 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:44,272 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:45,290 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:45,292 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:49,818 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:49,819 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:51,325 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:51,326 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:56,831 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:56,832 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:26:59,338 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:26:59,339 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:27:18,423 - __main__ - INFO - bot.py:28 - Bot initialized successfully
2025-07-08 22:27:18,423 - __main__ - INFO - bot.py:289 - Starting bot polling...
2025-07-08 22:28:49,029 - smm_api - INFO - smm_api.py:46 - Adding payment for user 1630035459, username: mlbbmoontod0, amount: 1.0, memo: 597540791206274411f4e80997e862e4
2025-07-08 22:28:52,064 - smm_api - INFO - smm_api.py:77 - API Response Status: 200
2025-07-08 22:28:52,065 - smm_api - INFO - smm_api.py:78 - API Response Content-Type: text/html; charset=UTF-8
2025-07-08 22:28:52,065 - smm_api - INFO - smm_api.py:79 - API Response Body (first 200 chars):     <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compati
2025-07-08 22:28:52,065 - smm_api - WARNING - smm_api.py:93 - API returned HTML interface - payment may have been processed
2025-07-08 22:44:18,888 - TeleBot - ERROR - __init__.py:960 - Infinity polling exception: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))
2025-07-08 22:44:18,895 - TeleBot - ERROR - __init__.py:962 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1428, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1428, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
urllib3.exceptions.ProtocolError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 955, in infinity_polling
    self.polling(non_stop=True, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 logger_level=logger_level, allowed_updates=allowed_updates, restart_on_change=False,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 *args, **kwargs)
                 ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1043, in polling
    self.__threaded_polling(non_stop=non_stop, interval=interval, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                            logger_level=logger_level, allowed_updates=allowed_updates)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1118, in __threaded_polling
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 156, in _make_request
    result = _get_req_session().request(
        method, request_url, params=params, files=files,
        timeout=(connect_timeout, read_timeout), proxies=proxy)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 501, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))

2025-07-08 22:49:57,720 - __main__ - INFO - bot.py:28 - Bot initialized successfully
2025-07-08 22:49:57,721 - __main__ - INFO - bot.py:289 - Starting bot polling...
2025-07-08 22:49:59,538 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:49:59,540 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:50:03,010 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:50:03,011 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:50:03,473 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:50:03,474 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:50:07,233 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:50:07,234 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:50:07,960 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:50:07,962 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:50:12,174 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:50:12,176 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:50:13,389 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-08 22:50:13,390 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-08 22:52:06,649 - __main__ - INFO - bot.py:28 - Bot initialized successfully
2025-07-08 22:52:06,649 - __main__ - INFO - bot.py:289 - Starting bot polling...
2025-07-08 22:53:33,989 - smm_api - INFO - smm_api.py:47 - Adding payment for user 1630035459, username: mlbbmoontod0, amount: 1.0, memo: ccde2f4777c742a78681a927ad24a64b
2025-07-08 22:53:35,467 - smm_api - INFO - smm_api.py:65 - API Response Status: 200
2025-07-08 22:53:35,468 - smm_api - INFO - smm_api.py:66 - API Response Content-Type: text/html; charset=UTF-8
2025-07-08 22:53:35,468 - smm_api - INFO - smm_api.py:67 - API Response Body:     <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="csrf-param" content="_csrf_admin">
<meta name="csrf-token" content="9nEztJgtICEUxh1b5hl7tcAiblHLzfTctMjGyR2fcI2CNGf2oB9uYnCgUxOCYBmG83glMo-Us7XjkLaxMK8a6g==">
        <title></title>
                    <link rel="shortcut icon" type="image/ico" href=""/>
                <link href="/react_apps/admin/static/css/main.6ec61889d8c5d015738f.css" rel="stylesheet">        <script> window.modules = {}; </script>
    </head>

    <body class="">

    
    <noscript>You need to enable JavaScript to run this app.</noscript>

    
<script>
    window.appConfig = {};
    window.appConfig = {"second_factor":true,"captcha_required":false,"captcha_site_key":"6Len5bUaAAAAAArhgiBHe_DhW5XsSygGh5JfR0yF","captcha_type":"g-recaptcha","is_dev":false,"posthog":{"apikey":"phc_dyafmP9dhK1Ix71AaOFYhGBGPzpWY6FtPav5gD5stlY","domain":"chhean-smm.net","admin":null,"params":{"api_host":"https:\/\/app.posthog.com","autocapture":false,"capture_pageview":false}},"websocket_url":null,"reserve_websocket_url":null};
</script>

<div id="root">
    <div style="position: absolute; top: 0; left: 0; width: 100%; min-height: 100vh; display: flex; align-items: center; justify-content: center">
        <div class="spinner"></div>
    </div>
    <style>
        .spinner {
            display: inline-block;
            width: 32px;
            height: 32px;
            --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg%3E%3Ccircle cx='3' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='21' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='21' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='3' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='5.64' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='5.64' r='2' fill='%23000'/%3E%3CanimateTransform attributeName='transform' dur='1.5s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/g%3E%3C/svg%3E");
            background-color: currentColor;
            -webkit-mask-image: var(--svg);
            mask-image: var(--svg);
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: 100% 100%;
            mask-size: 100% 100%;
        }
    </style>
</div>
    <script src="/builds/posthog/js/posthog.7e8c35b8acc75587bb73.js"></script>
<script src="/builds/posthog/js/posthog-front.js"></script>
<script src="/react_apps/admin/static/js/main.46262c4695c08d79e7bd.js"></script>
<script src="/react_apps/admin/static/js/vendors.e1966d222e648233c5e1.js"></script>
<script>window.modules.customController = {"auth":0};</script>
    </body>
    </html>

2025-07-08 22:53:35,468 - smm_api - ERROR - smm_api.py:81 - Failed to parse success response: Expecting value: line 1 column 5 (char 4)
2025-07-08 23:39:13,710 - smm_api - INFO - smm_api.py:47 - Adding payment for user 1630035459, username: mlbbmoontod0, amount: 1.0, memo: ac454a6b9f4e80b5ebc67e0abaac3909
2025-07-08 23:39:15,018 - smm_api - INFO - smm_api.py:65 - API Response Status: 200
2025-07-08 23:39:15,019 - smm_api - INFO - smm_api.py:66 - API Response Content-Type: text/html; charset=UTF-8
2025-07-08 23:39:15,019 - smm_api - INFO - smm_api.py:67 - API Response Body:     <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="csrf-param" content="_csrf_admin">
<meta name="csrf-token" content="j19ii0L95N58TgiDQE_MyJM59quS-EmGiJzB3DvTSTP7GjbJes-qnRgoRsskNq77oGO9yNahDu_fxLGkFuMjVA==">
        <title></title>
                    <link rel="shortcut icon" type="image/ico" href=""/>
                <link href="/react_apps/admin/static/css/main.6ec61889d8c5d015738f.css" rel="stylesheet">        <script> window.modules = {}; </script>
    </head>

    <body class="">

    
    <noscript>You need to enable JavaScript to run this app.</noscript>

    
<script>
    window.appConfig = {};
    window.appConfig = {"second_factor":true,"captcha_required":false,"captcha_site_key":"6Len5bUaAAAAAArhgiBHe_DhW5XsSygGh5JfR0yF","captcha_type":"g-recaptcha","is_dev":false,"posthog":{"apikey":"phc_dyafmP9dhK1Ix71AaOFYhGBGPzpWY6FtPav5gD5stlY","domain":"chhean-smm.net","admin":null,"params":{"api_host":"https:\/\/app.posthog.com","autocapture":false,"capture_pageview":false}},"websocket_url":null,"reserve_websocket_url":null};
</script>

<div id="root">
    <div style="position: absolute; top: 0; left: 0; width: 100%; min-height: 100vh; display: flex; align-items: center; justify-content: center">
        <div class="spinner"></div>
    </div>
    <style>
        .spinner {
            display: inline-block;
            width: 32px;
            height: 32px;
            --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg%3E%3Ccircle cx='3' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='21' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='21' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='3' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='5.64' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='5.64' r='2' fill='%23000'/%3E%3CanimateTransform attributeName='transform' dur='1.5s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/g%3E%3C/svg%3E");
            background-color: currentColor;
            -webkit-mask-image: var(--svg);
            mask-image: var(--svg);
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: 100% 100%;
            mask-size: 100% 100%;
        }
    </style>
</div>
    <script src="/builds/posthog/js/posthog.7e8c35b8acc75587bb73.js"></script>
<script src="/builds/posthog/js/posthog-front.js"></script>
<script src="/react_apps/admin/static/js/main.46262c4695c08d79e7bd.js"></script>
<script src="/react_apps/admin/static/js/vendors.e1966d222e648233c5e1.js"></script>
<script>window.modules.customController = {"auth":0};</script>
    </body>
    </html>

2025-07-08 23:39:15,020 - smm_api - ERROR - smm_api.py:81 - Failed to parse success response: Expecting value: line 1 column 5 (char 4)
2025-07-08 23:39:44,713 - smm_api - ERROR - smm_api.py:180 - Failed to get user balance: 500 Server Error: Internal Server Error for url: https://chhean-smm.net/api/v2
2025-07-08 23:39:55,041 - smm_api - INFO - smm_api.py:47 - Adding payment for user 1630035459, username: teytakakvitya3.biu, amount: 1.0, memo: 593a03c1b49106aa080fe6fe4e88756b
2025-07-08 23:39:55,469 - smm_api - INFO - smm_api.py:65 - API Response Status: 200
2025-07-08 23:39:55,470 - smm_api - INFO - smm_api.py:66 - API Response Content-Type: text/html; charset=UTF-8
2025-07-08 23:39:55,470 - smm_api - INFO - smm_api.py:67 - API Response Body:     <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="csrf-param" content="_csrf_admin">
<meta name="csrf-token" content="ZQ7_Uo-3Tecdi8r4BRsWs6PTQentS5oKX2v5p_BZSe8RS6sQt4UDpHnthLBhYnSAkIkKiqkS3WMIM4nf3WkjiA==">
        <title></title>
                    <link rel="shortcut icon" type="image/ico" href=""/>
                <link href="/react_apps/admin/static/css/main.6ec61889d8c5d015738f.css" rel="stylesheet">        <script> window.modules = {}; </script>
    </head>

    <body class="">

    
    <noscript>You need to enable JavaScript to run this app.</noscript>

    
<script>
    window.appConfig = {};
    window.appConfig = {"second_factor":true,"captcha_required":false,"captcha_site_key":"6Len5bUaAAAAAArhgiBHe_DhW5XsSygGh5JfR0yF","captcha_type":"g-recaptcha","is_dev":false,"posthog":{"apikey":"phc_dyafmP9dhK1Ix71AaOFYhGBGPzpWY6FtPav5gD5stlY","domain":"chhean-smm.net","admin":null,"params":{"api_host":"https:\/\/app.posthog.com","autocapture":false,"capture_pageview":false}},"websocket_url":null,"reserve_websocket_url":null};
</script>

<div id="root">
    <div style="position: absolute; top: 0; left: 0; width: 100%; min-height: 100vh; display: flex; align-items: center; justify-content: center">
        <div class="spinner"></div>
    </div>
    <style>
        .spinner {
            display: inline-block;
            width: 32px;
            height: 32px;
            --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg%3E%3Ccircle cx='3' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='21' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='21' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='3' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='5.64' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='5.64' r='2' fill='%23000'/%3E%3CanimateTransform attributeName='transform' dur='1.5s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/g%3E%3C/svg%3E");
            background-color: currentColor;
            -webkit-mask-image: var(--svg);
            mask-image: var(--svg);
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: 100% 100%;
            mask-size: 100% 100%;
        }
    </style>
</div>
    <script src="/builds/posthog/js/posthog.7e8c35b8acc75587bb73.js"></script>
<script src="/builds/posthog/js/posthog-front.js"></script>
<script src="/react_apps/admin/static/js/main.46262c4695c08d79e7bd.js"></script>
<script src="/react_apps/admin/static/js/vendors.e1966d222e648233c5e1.js"></script>
<script>window.modules.customController = {"auth":0};</script>
    </body>
    </html>

2025-07-08 23:39:55,471 - smm_api - ERROR - smm_api.py:81 - Failed to parse success response: Expecting value: line 1 column 5 (char 4)
2025-07-08 23:40:57,000 - __main__ - INFO - bot.py:28 - Bot initialized successfully
2025-07-08 23:40:57,000 - __main__ - INFO - bot.py:289 - Starting bot polling...
2025-07-08 23:41:23,280 - smm_api - INFO - smm_api.py:47 - Adding payment for user 1630035459, username: mlbbmoontod0, amount: 1.0, memo: 6fd7f747d08efa91df54fcd7b82d9596
2025-07-08 23:41:24,613 - smm_api - INFO - smm_api.py:65 - API Response Status: 200
2025-07-08 23:41:24,613 - smm_api - INFO - smm_api.py:66 - API Response Content-Type: text/html; charset=UTF-8
2025-07-08 23:41:24,613 - smm_api - INFO - smm_api.py:67 - API Response Body:     <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="csrf-param" content="_csrf_admin">
<meta name="csrf-token" content="TNphwIcKOFX3oQgqaNIYcAGIaNqYSTtXhzWuJJEEYvYdlxGzt2JSYKGRSUAc5VcnRL1fst8AanrjfPt1-1cksQ==">
        <title></title>
                    <link rel="shortcut icon" type="image/ico" href=""/>
                <link href="/react_apps/admin/static/css/main.6ec61889d8c5d015738f.css" rel="stylesheet">        <script> window.modules = {}; </script>
    </head>

    <body class="">

    
    <noscript>You need to enable JavaScript to run this app.</noscript>

    
<script>
    window.appConfig = {};
    window.appConfig = {"second_factor":true,"captcha_required":false,"captcha_site_key":"6Len5bUaAAAAAArhgiBHe_DhW5XsSygGh5JfR0yF","captcha_type":"g-recaptcha","is_dev":false,"posthog":{"apikey":"phc_dyafmP9dhK1Ix71AaOFYhGBGPzpWY6FtPav5gD5stlY","domain":"chhean-smm.net","admin":null,"params":{"api_host":"https:\/\/app.posthog.com","autocapture":false,"capture_pageview":false}},"websocket_url":null,"reserve_websocket_url":null};
</script>

<div id="root">
    <div style="position: absolute; top: 0; left: 0; width: 100%; min-height: 100vh; display: flex; align-items: center; justify-content: center">
        <div class="spinner"></div>
    </div>
    <style>
        .spinner {
            display: inline-block;
            width: 32px;
            height: 32px;
            --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg%3E%3Ccircle cx='3' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='21' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='21' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='3' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='5.64' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='5.64' r='2' fill='%23000'/%3E%3CanimateTransform attributeName='transform' dur='1.5s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/g%3E%3C/svg%3E");
            background-color: currentColor;
            -webkit-mask-image: var(--svg);
            mask-image: var(--svg);
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: 100% 100%;
            mask-size: 100% 100%;
        }
    </style>
</div>
    <script src="/builds/posthog/js/posthog.7e8c35b8acc75587bb73.js"></script>
<script src="/builds/posthog/js/posthog-front.js"></script>
<script src="/react_apps/admin/static/js/main.46262c4695c08d79e7bd.js"></script>
<script src="/react_apps/admin/static/js/vendors.e1966d222e648233c5e1.js"></script>
<script>window.modules.customController = {"auth":0};</script>
    </body>
    </html>

2025-07-08 23:41:24,614 - smm_api - INFO - smm_api.py:82 - Payment successful - API returned HTML admin interface
2025-07-08 23:49:18,431 - TeleBot - ERROR - __init__.py:960 - Infinity polling exception: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))
2025-07-08 23:49:18,437 - TeleBot - ERROR - __init__.py:962 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1428, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\util\util.py", line 38, in reraise
    raise value.with_traceback(tb)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 1428, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
urllib3.exceptions.ProtocolError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 955, in infinity_polling
    self.polling(non_stop=True, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 logger_level=logger_level, allowed_updates=allowed_updates, restart_on_change=False,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 *args, **kwargs)
                 ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1043, in polling
    self.__threaded_polling(non_stop=non_stop, interval=interval, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                            logger_level=logger_level, allowed_updates=allowed_updates)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1118, in __threaded_polling
    raise e
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 156, in _make_request
    result = _get_req_session().request(
        method, request_url, params=params, files=files,
        timeout=(connect_timeout, read_timeout), proxies=proxy)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\adapters.py", line 501, in send
    raise ConnectionError(err, request=request)
requests.exceptions.ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))

2025-07-09 00:03:41,066 - __main__ - INFO - bot.py:28 - Bot initialized successfully
2025-07-09 00:03:41,066 - __main__ - INFO - bot.py:289 - Starting bot polling...
2025-07-09 00:03:44,753 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 00:03:44,755 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-09 00:03:48,548 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 00:03:48,549 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-09 00:03:49,426 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 00:03:49,427 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-09 00:03:53,446 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 00:03:53,447 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-09 00:03:54,459 - TeleBot - ERROR - __init__.py:1083 - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-09 00:03:54,460 - TeleBot - ERROR - __init__.py:1085 - Exception traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 1073, in __threaded_polling
    polling_thread.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 108, in raise_exceptions
    raise self.exception_info
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\util.py", line 90, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 649, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\__init__.py", line 623, in get_updates
    json_updates = apihelper.get_updates(self.token, offset, limit, timeout, allowed_updates, long_polling_timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 321, in get_updates
    return _make_request(token, method_url, params=payload)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 162, in _make_request
    json_result = _check_result(method_name, result)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\telebot\apihelper.py", line 189, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 409. Description: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running

2025-07-09 00:04:28,449 - __main__ - INFO - bot.py:28 - Bot initialized successfully
2025-07-09 00:04:28,449 - __main__ - INFO - bot.py:289 - Starting bot polling...
2025-07-09 00:05:53,875 - smm_api - INFO - smm_api.py:62 - Adding payment for user 1630035459, username: mlbbmoontod0, amount: 1.0, memo: 37080cb2767cb2efde2660b7426fd603
2025-07-09 00:05:56,903 - smm_api - INFO - smm_api.py:80 - API Response Status: 200
2025-07-09 00:05:56,932 - smm_api - INFO - smm_api.py:81 - API Response Content-Type: text/html; charset=UTF-8
2025-07-09 00:05:56,932 - smm_api - INFO - smm_api.py:82 - API Response Body:     <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="csrf-param" content="_csrf_admin">
<meta name="csrf-token" content="G1j2dhfthcQFywqb8Gx_e4N1yBzg_quyt99dKbXW9ihqYI4cT6Tyg1GiP9TJLkgJ8zzwbpO27cXjmh9zxLeuQQ==">
        <title></title>
                    <link rel="shortcut icon" type="image/ico" href=""/>
                <link href="/react_apps/admin/static/css/main.6ec61889d8c5d015738f.css" rel="stylesheet">        <script> window.modules = {}; </script>
    </head>

    <body class="">

    
    <noscript>You need to enable JavaScript to run this app.</noscript>

    
<script>
    window.appConfig = {};
    window.appConfig = {"second_factor":true,"captcha_required":false,"captcha_site_key":"6Len5bUaAAAAAArhgiBHe_DhW5XsSygGh5JfR0yF","captcha_type":"g-recaptcha","is_dev":false,"posthog":{"apikey":"phc_dyafmP9dhK1Ix71AaOFYhGBGPzpWY6FtPav5gD5stlY","domain":"chhean-smm.net","admin":null,"params":{"api_host":"https:\/\/app.posthog.com","autocapture":false,"capture_pageview":false}},"websocket_url":null,"reserve_websocket_url":null};
</script>

<div id="root">
    <div style="position: absolute; top: 0; left: 0; width: 100%; min-height: 100vh; display: flex; align-items: center; justify-content: center">
        <div class="spinner"></div>
    </div>
    <style>
        .spinner {
            display: inline-block;
            width: 32px;
            height: 32px;
            --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cg%3E%3Ccircle cx='3' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='21' cy='12' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='21' r='2' fill='%23000'/%3E%3Ccircle cx='12' cy='3' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='5.64' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='5.64' cy='18.36' r='2' fill='%23000'/%3E%3Ccircle cx='18.36' cy='5.64' r='2' fill='%23000'/%3E%3CanimateTransform attributeName='transform' dur='1.5s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/g%3E%3C/svg%3E");
            background-color: currentColor;
            -webkit-mask-image: var(--svg);
            mask-image: var(--svg);
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            -webkit-mask-size: 100% 100%;
            mask-size: 100% 100%;
        }
    </style>
</div>
    <script src="/builds/posthog/js/posthog-front.js"></script>
<script src="/builds/posthog/js/posthog.7e8c35b8acc75587bb73.js"></script>
<script src="/react_apps/admin/static/js/main.46262c4695c08d79e7bd.js"></script>
<script src="/react_apps/admin/static/js/vendors.e1966d222e648233c5e1.js"></script>
<script>window.modules.customController = {"auth":0};</script>
    </body>
    </html>

2025-07-09 00:05:56,933 - smm_api - INFO - smm_api.py:97 - Payment successful - API returned HTML admin interface
