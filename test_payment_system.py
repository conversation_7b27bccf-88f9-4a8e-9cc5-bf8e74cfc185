#!/usr/bin/env python3
"""
Test script for SMM Payment System

This script tests the payment functionality with different user identification methods.
Run this script to verify your API configuration and payment system is working correctly.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smm_api import SMMAPIClient
from utils import setup_logging
import logging

def test_api_connection():
    """Test basic API connection"""
    print("🔍 Testing API connection...")
    
    client = SMMAPIClient()
    
    if client.test_connection():
        print("✅ API connection successful!")
        return True
    else:
        print("❌ API connection failed!")
        return False

def test_payment_by_username():
    """Test payment addition using username"""
    print("\n💰 Testing payment by username...")
    
    client = SMMAPIClient()
    
    # Test with a small amount
    result = client.add_payment(
        user_id="test_user_123",
        amount=1.00,
        username="test_username",  # Replace with actual username
        payment_method="KHQR"
    )
    
    if result['success']:
        print("✅ Payment by username successful!")
        print(f"   Response: {result['data']}")
        return True
    else:
        print("❌ Payment by username failed!")
        print(f"   Error: {result['message']}")
        return False

def test_payment_by_user_id():
    """Test payment addition using user ID"""
    print("\n💰 Testing payment by user ID...")
    
    client = SMMAPIClient()
    
    # Test with a small amount
    result = client.add_payment_by_user_id(
        telegram_user_id="test_user_123",
        amount=1.00,
        smm_user_id=1,  # Replace with actual user ID
        payment_method="KHQR"
    )
    
    if result['success']:
        print("✅ Payment by user ID successful!")
        print(f"   Response: {result['data']}")
        return True
    else:
        print("❌ Payment by user ID failed!")
        print(f"   Error: {result['message']}")
        return False

def test_payment_by_email():
    """Test payment addition using email"""
    print("\n💰 Testing payment by email...")
    
    client = SMMAPIClient()
    
    # Test with a small amount
    result = client.add_payment_by_email(
        telegram_user_id="test_user_123",
        amount=1.00,
        user_email="<EMAIL>",  # Replace with actual email
        payment_method="KHQR"
    )
    
    if result['success']:
        print("✅ Payment by email successful!")
        print(f"   Response: {result['data']}")
        return True
    else:
        print("❌ Payment by email failed!")
        print(f"   Error: {result['message']}")
        return False

def test_input_validation():
    """Test input validation"""
    print("\n🔍 Testing input validation...")
    
    client = SMMAPIClient()
    
    # Test invalid amount
    result = client.add_payment(
        user_id="test_user_123",
        amount=-5.00,  # Invalid negative amount
        username="test_username"
    )
    
    if not result['success'] and 'amount' in result['error'].lower():
        print("✅ Negative amount validation working!")
    else:
        print("❌ Negative amount validation failed!")
        return False
    
    # Test invalid username
    result = client.add_payment(
        user_id="test_user_123",
        amount=10.00,
        username=""  # Invalid empty username
    )
    
    if not result['success'] and 'username' in result['error'].lower():
        print("✅ Empty username validation working!")
    else:
        print("❌ Empty username validation failed!")
        return False
    
    # Test invalid email
    result = client.add_payment_by_email(
        telegram_user_id="test_user_123",
        amount=10.00,
        user_email="invalid-email"  # Invalid email format
    )
    
    if not result['success'] and 'email' in result['error'].lower():
        print("✅ Invalid email validation working!")
    else:
        print("❌ Invalid email validation failed!")
        return False
    
    return True

def test_balance_check():
    """Test balance checking functionality"""
    print("\n💳 Testing balance check...")
    
    client = SMMAPIClient()
    
    result = client.get_user_balance("test_user_123")
    
    if result['success']:
        print("✅ Balance check successful!")
        print(f"   Balance: {result['data']}")
        return True
    else:
        print("❌ Balance check failed!")
        print(f"   Error: {result['message']}")
        return False

def main():
    """Main test function"""
    print("🚀 SMM Payment System Test Suite")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    
    # Check environment variables
    if not os.getenv('SMM_API_KEY'):
        print("❌ SMM_API_KEY environment variable not set!")
        print("   Please set your API key in .env file or environment variables")
        return False
    
    print(f"🔑 API Key: {'*' * 20}{os.getenv('SMM_API_KEY')[-4:]}")
    print(f"🌐 Base URL: {os.getenv('SMM_API_BASE_URL', 'https://chhean-smm.net/admin/adminapi/v2')}")
    
    # Run tests
    tests = [
        ("API Connection", test_api_connection),
        ("Balance Check", test_balance_check),
        ("Input Validation", test_input_validation),
        # Uncomment these when you have valid test data:
        # ("Payment by Username", test_payment_by_username),
        # ("Payment by User ID", test_payment_by_user_id),
        # ("Payment by Email", test_payment_by_email),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            logging.error(f"Test {test_name} failed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Payment system is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the logs for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
