# Telegram SMM Bot

A Telegram bot for processing SMM (Social Media Marketing) top-ups through the chhean-smm.net API.

## Features

- 💰 **Top-up Processing**: Add funds to SMM accounts via Telegram
- 💳 **Balance Checking**: Check current account balance
- ✅ **Payment Confirmation**: Interactive payment confirmation system
- 🔒 **Secure API Integration**: Secure communication with SMM API
- 📝 **Comprehensive Logging**: Detailed logging for debugging and monitoring
- ❌ **Error Handling**: Robust error handling with user-friendly messages

## Setup Instructions

### 1. Prerequisites

- Python 3.8 or higher
- A Telegram Bot Token (from @BotFather)
- SMM API Key from chhean-smm.net

### 2. Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### 3. Configuration

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file with your credentials:
   ```
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
   SMM_API_KEY=your_smm_api_key_here
   SMM_API_BASE_URL=https://chhean-smm.net/admin/adminapi/v2
   ```

### 4. Getting Your Bot Token

1. Message @BotFather on Telegram
2. Send `/newbot` command
3. Follow the instructions to create your bot
4. Copy the bot token to your `.env` file

### 5. Getting Your SMM API Key

Contact the SMM service provider to obtain your API key.

## Running the Bot

```bash
python bot.py
```

The bot will start and begin polling for messages. You should see:
```
INFO - Starting bot polling...
```

## Bot Commands

- `/start` - Show welcome message and available commands
- `/topup` - Start the top-up process
- `/balance` - Check current account balance
- `/help` - Show help information

## Usage Flow

1. User sends `/topup` command
2. Bot asks for the amount to top up
3. User enters amount (e.g., "10.00")
4. Bot shows confirmation with amount and user details
5. User clicks "✅ Confirm Payment" button
6. Bot processes payment through SMM API
7. Bot shows success/failure message

## File Structure

```
├── bot.py              # Main bot application
├── config.py           # Configuration management
├── smm_api.py         # SMM API client
├── utils.py           # Utility functions and error handling
├── requirements.txt   # Python dependencies
├── .env.example      # Environment variables template
├── README.md         # This file
└── bot.log           # Log file (created when bot runs)
```

## API Integration

The bot integrates with the SMM API endpoint:
- **Endpoint**: `POST /paths/payments-add`
- **Authentication**: Bearer token via API key
- **Payload**: User ID, amount, payment method

## Error Handling

The bot includes comprehensive error handling:
- Input validation for amounts
- API error handling with user-friendly messages
- Logging of all errors for debugging
- Graceful handling of network issues

## Logging

Logs are written to:
- `bot.log` file (detailed logs)
- Console (important messages only)

Log levels:
- DEBUG: Detailed information for debugging
- INFO: General information about bot operation
- WARNING: Warning messages
- ERROR: Error messages

## Security Considerations

- API keys are stored in environment variables
- User input is validated and sanitized
- Secure HTTPS communication with APIs
- No sensitive data logged

## Troubleshooting

### Bot doesn't start
- Check that `TELEGRAM_BOT_TOKEN` is set correctly
- Verify internet connection
- Check bot.log for error messages

### Payments fail
- Verify `SMM_API_KEY` is correct
- Check SMM API service status
- Review API response in logs

### Commands don't work
- Ensure bot is added to the chat
- Check if bot has necessary permissions
- Verify bot token is valid

## Support

For issues with:
- **Bot functionality**: Check the logs and error messages
- **SMM API**: Contact the SMM service provider
- **Telegram Bot API**: Refer to Telegram Bot API documentation

## License

This project is for educational and commercial use. Please ensure you have proper authorization to use the SMM API.
