import logging
import functools
import traceback
from typing import Callable, Any

def setup_logging():
    """Set up comprehensive logging configuration"""
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Create handlers
    # File handler for detailed logs
    file_handler = logging.FileHandler('bot.log', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    
    # Console handler for important messages
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Reduce noise from external libraries
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)

def error_handler(func: Callable) -> Callable:
    """Decorator for handling errors in bot functions"""
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger = logging.getLogger(func.__module__)
            logger.error(f"Error in {func.__name__}: {e}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            raise
    
    return wrapper

def validate_amount(amount_str: str) -> tuple[bool, float, str]:
    """
    Validate payment amount
    
    Returns:
        (is_valid, amount, error_message)
    """
    try:
        amount = float(amount_str.strip())
        
        if amount <= 0:
            return False, 0, "Amount must be greater than 0"
        
        if amount > 10000:
            return False, 0, "Maximum amount is $10,000"
        
        if amount < 1:
            return False, 0, "Minimum amount is $1"
        
        # Check for reasonable decimal places
        if len(str(amount).split('.')[-1]) > 2:
            return False, 0, "Amount can have maximum 2 decimal places"
        
        return True, amount, ""
        
    except ValueError:
        return False, 0, "Please enter a valid number"

def format_currency(amount: float) -> str:
    """Format amount as currency"""
    return f"${amount:.2f}"

def sanitize_user_input(text: str) -> str:
    """Sanitize user input to prevent injection attacks"""
    if not text:
        return ""
    
    # Remove potentially dangerous characters
    dangerous_chars = ['<', '>', '"', "'", '&', '\n', '\r', '\t']
    sanitized = text
    
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, '')
    
    return sanitized.strip()

class BotError(Exception):
    """Custom exception for bot-specific errors"""
    
    def __init__(self, message: str, user_message: str = None):
        super().__init__(message)
        self.user_message = user_message or "An error occurred. Please try again."

class APIError(BotError):
    """Exception for API-related errors"""
    pass

class ValidationError(BotError):
    """Exception for validation errors"""
    pass
